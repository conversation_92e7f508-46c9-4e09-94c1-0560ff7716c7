package org.dromara.business.domain.bo;

import org.dromara.business.domain.SecEventFlow;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 事件流程业务对象 sec_event_flow
 *
 * <AUTHOR> Li
 * @date 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SecEventFlow.class, reverseConvertGenerate = false)
public class SecEventFlowBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 事件id
     */
    @NotNull(message = "事件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long eventId;

    /**
     * 事件阶段，1事件通报，2事件处置，3事件初审，4事件终审，5事件复审
     */
    @NotNull(message = "事件阶段，1事件通报，2事件处置，3事件初审，4事件终审，5事件复审不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long stage;

    /**
     * 事件状态，stage=1，1已通报;
stage=2，-2非本单位资产，-1误报，1已处理，2事件转发；
stage=3，同4；
stage=4，-1误报，1审核不通过，2已修复，3网站关闭，4无法访问，5限制内网访问，6无法复现；
stage=5，1重新通报，2无法复现，3已修复
     */
    @NotNull(message = "事件状态，stage=1，1已通报; stage=2，-2非本单位资产，-1误报，1已处理，2事件转发； stage=3，同4； stage=4，-1误报，1审核不通过，2已修复，3网站关闭，4无法访问，5限制内网访问，6无法复现； stage=5，1重新通报，2无法复现，3已修复不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dealStatus;

    /**
     * 说明
     */
    @NotBlank(message = "说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 经办人
     */
    @NotBlank(message = "经办人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operator;

    /**
     * 文件附件
     */
    @NotBlank(message = "文件附件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileUrl;

    /**
     * 经办时间
     */
    private Date handlingTime;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
