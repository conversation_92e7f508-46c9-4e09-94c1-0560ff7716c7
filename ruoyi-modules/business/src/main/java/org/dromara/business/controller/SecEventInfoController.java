package org.dromara.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.business.domain.bo.SecEventFlowDealBo;
import org.dromara.business.domain.bo.SecEventInfoBo;
import org.dromara.business.domain.vo.*;
import org.dromara.business.service.ISecEventInfoService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 安全事件信息
 *
 * <AUTHOR> Li
 * @date 2024-07-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/eventInfo")
public class SecEventInfoController extends BaseController {

    private final ISecEventInfoService secEventInfoService;

    /**
     * 查询威胁通报数量
     * 待处置：nextdeptid是自己的机构
     * 待审核：高校：当前节点为自身；省厅：当前节点为自身，且stage为3
     * 已完成：高校：通报对象为自身，且stage=4；省厅：stage=4
     */
    @GetMapping("/report/count")
    public R<SecEventFlowNumVo> reportCount() {
        SecEventFlowNumVo vo = secEventInfoService.getReportCount();
        return R.ok(vo);
    }

    /**
     * 查询威胁通报
     * @param vo
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("business:eventInfo:list")
    @GetMapping("/reportlist")
    public TableDataInfo<SecEventReportVo> reportPageList(SecEventReportRequestVo vo, PageQuery pageQuery) {
        return secEventInfoService.reportPageList(vo, pageQuery);
    }

    /**
     * 查询威胁检索列表
     */
    @SaCheckPermission("business:eventInfo:list")
    @GetMapping("/list")
    public TableDataInfo<SecEventInfoListVo> list(SecEventInfoRequestVo vo, PageQuery pageQuery) {
        return secEventInfoService.querySearchPageList(vo, pageQuery);
    }

    /**
     * 导出安全事件信息列表
     */
    @SaCheckPermission("business:eventInfo:export")
    @Log(title = "安全事件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SecEventInfoRequestVo bo, HttpServletResponse response) {
        List<SecEventInfoExportVo> list = secEventInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "安全事件信息", SecEventInfoExportVo.class, response);
    }

    /**
     * 获取安全事件信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:eventInfo:query")
    @GetMapping("/{id}")
    public R<SecEventInfoAndFlowVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(secEventInfoService.queryById(id));
    }

    /**
     * 新增安全事件信息
     */
    @SaCheckPermission("business:eventInfo:add")
    @Log(title = "安全事件信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SecEventInfoBo bo) {
        return toAjax(secEventInfoService.insertByBo(bo));
    }

    /**
     * 修改安全事件信息
     */
    @SaCheckPermission("business:eventInfo:edit")
    @Log(title = "安全事件信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SecEventInfoBo bo) {
        return toAjax(secEventInfoService.updateByBo(bo));
    }

    /**
     * 处理安全事件流程
     * 1事件通报，2事件处置，3事件初审，4事件终审，5事件复审
     * 前置条件：判断当前处置单位和提交单位是否匹配
     * 1.流程节点为事件通报（处理单位为省厅或高校）
     *  1.1.处理方式（处置）：-2非本单位资产，-1误报，1已处理，2事件转发
     *  1.2.省厅：下一处理单位为教育部；
     *  1.3.高校：下一处理单位为省厅
     * 2.流程节点为事件处置（处理单位为省厅或高校）
     *  2.1.省厅处理方式（初审）：-1误报，1审核不通过，2已修复，3网站关闭，4无法访问，5限制内网访问，6无法复现
     *  2.2.高校处理方式（处置）：-2非本单位资产，-1误报，1已处理，2事件转发
     *  2.3.省厅：审核不通过，下一处理单位为高校；其他，下一处理单位为教育部；
     *  2.4.高校：下一处理单位为省厅
     * 3.流程节点为事件初审（处理单位为教育部）
     *  3.1.无法处理
     * 4.流程节点为事件终审（完成）
     */
    @Log(title = "安全事件流程更新", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/deal")
    public R<Void> deal(@Validated(EditGroup.class) @RequestBody SecEventFlowDealBo bo) {
        return toAjax(secEventInfoService.dealFlowByBo(bo));
    }

    /**
     * 删除安全事件信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:eventInfo:remove")
    @Log(title = "安全事件信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(secEventInfoService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取安全事件信息详细信息
     *
     * @param type 威胁大类
     */
    @GetMapping("/type/{type}")
    public R<List<SecEventSubType>> getType(@NotNull(message = "威胁类型不能为空")
                                     @PathVariable String type) {
        return R.ok(secEventInfoService.querySubTypeList(type));
    }

    /**
     * 获取事件来源单位信息
     */
    @GetMapping("/dept/source")
    public R<List<SecEventSourceDeptVo>> getSourceDept() {
        return R.ok(secEventInfoService.getSourceDept());
    }

    /**
     * 获取事件来源单位信息
     */
    @GetMapping("/dept/source_having")
    public R<List<SecEventSourceDeptVo>> getSourceDeptByHaving() {
        return R.ok(secEventInfoService.getSourceDeptByHaving());
    }

    /**
     * 一键催办
     * 1.根据id查询任务（获取威胁机构id和当前待处理机构id）
     * 2.判断威胁机构id和当前处理机构id是否为同一个机构
     * 3.下发短信
     */
    @SaCheckPermission("business:eventInfo:urging")
    @GetMapping("/urging/{ids}")
    public R<Void> urging(@PathVariable Long[] ids) {
        return toAjax(secEventInfoService.urging(List.of(ids)));
    }
}
